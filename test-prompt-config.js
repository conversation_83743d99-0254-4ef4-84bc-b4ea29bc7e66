// Test script to verify prompt configuration is working
const testPromptConfig = async () => {
  try {
    console.log('Testing prompt configuration...');
    
    // Test with custom prompt
    const response = await fetch('http://localhost:3000/api/chat/openai-realtime', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Cookie': 'your-session-cookie-here' // You'll need to get this from browser
      },
      body: JSON.stringify({
        model: 'gpt-realtime',
        voice: 'ash',
        allowedMcpServers: {},
        promptConfig: 'pmpt_68b8274a94408197bd499e6388085cad05df5abc756129bc'
      })
    });
    
    if (response.ok) {
      const session = await response.json();
      console.log('Session created successfully');
      console.log('Instructions preview:', session.session?.instructions?.substring(0, 200));
      console.log('Full instructions length:', session.session?.instructions?.length);
      
      // Check if it contains the custom prompt content
      if (session.session?.instructions?.includes('Google L6 system design interviews')) {
        console.log('✅ Custom prompt is being used correctly!');
      } else {
        console.log('❌ Custom prompt is NOT being used');
      }
    } else {
      console.error('Failed to create session:', response.status, await response.text());
    }
    
  } catch (error) {
    console.error('Error testing prompt config:', error);
  }
};

// Note: This script needs to be run with proper authentication
console.log('This is a test script. Run it in browser console with proper session cookies.');
